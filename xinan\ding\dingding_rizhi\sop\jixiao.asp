<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->

<%
'获取URL参数
riqi = Request.QueryString("riqi")
xingming = Request.QueryString("xingming")
zhongwen_xingming = Request.QueryString("zhongwen_xingming")
guoqi_renwu_shuliang = Request.QueryString("guoqi_renwu_shuliang")
koufen = Request.QueryString("koufen")
koufen_mingxi = Request.QueryString("koufen_mingxi")
koufen_xitong = Request.QueryString("koufen_xitong")
operation = Request.QueryString("operation")
record_id = Request.QueryString("id")

'检查操作类型
If operation = "" Then
    Response.Write "{""code"":-1, ""msg"":""缺少操作类型参数""}"
    Response.End
End If

'添加记录操作
If operation = "add" Then
    '检查必填参数
    If xingming = "" Then
        Response.Write "{""code"":-1, ""msg"":""缺少姓名参数""}"
        Response.End
    End If
    
    '构建插入SQL
    Dim insert_sql
    Dim insert_fields()
    Dim insert_values()
    Dim field_count
    field_count = 0
    
    '动态构建插入字段和值
    If riqi <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[日期]"
        insert_values(field_count) = "'" & riqi & "'"
        field_count = field_count + 1
    End If
    
    If xingming <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[姓名]"
        insert_values(field_count) = "'" & xingming & "'"
        field_count = field_count + 1
    End If
    
    If zhongwen_xingming <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[中文姓名]"
        insert_values(field_count) = "'" & zhongwen_xingming & "'"
        field_count = field_count + 1
    End If
    
    If guoqi_renwu_shuliang <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[过期任务数量]"
        insert_values(field_count) = guoqi_renwu_shuliang
        field_count = field_count + 1
    End If
    
    If koufen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[扣分]"
        insert_values(field_count) = koufen
        field_count = field_count + 1
    End If
    
    If koufen_mingxi <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[扣分明细]"
        insert_values(field_count) = "'" & koufen_mingxi & "'"
        field_count = field_count + 1
    End If
    
    If koufen_xitong <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[扣分系统]"
        insert_values(field_count) = "'" & koufen_xitong & "'"
        field_count = field_count + 1
    End If
    
    '检查是否有字段需要插入
    If field_count = 0 Then
        Response.Write "{""code"":-1,""msg"":""没有需要插入的字段""}"
        Response.End
    End If
    
    '构建完整的插入SQL
    insert_sql = "INSERT INTO [待办任务扣分表] (" & Join(insert_fields, ", ") & ") VALUES (" & Join(insert_values, ", ") & ")"
    
    '执行插入操作
    On Error Resume Next
    conn.Execute insert_sql
    If Err.Number <> 0 Then
        Response.Write "{""code"":-1,""msg"":""插入失败: " & Err.Description & """}"
        Response.End
    End If
    On Error GoTo 0
    
    ' 返回成功响应
    Response.Write "{""code"":1,""msg"":""添加记录成功""}"

'更新记录操作
ElseIf operation = "update" Then
    '检查必填参数
    If record_id = "" Then
        Response.Write "{""code"":-1, ""msg"":""缺少记录ID参数""}"
        Response.End
    End If
    
    '先检查要更新的记录是否存在
    Dim rsCheck, sql
    sql = "SELECT COUNT(*) FROM [待办任务扣分表] WHERE id=" & record_id
    Set rsCheck = conn.Execute(sql)
    If rsCheck(0) = 0 Then
        Response.Write "{""code"":-1,""msg"":""要更新的记录不存在""}"
        Response.End
    End If
    Set rsCheck = Nothing
    
    '构建更新SQL
    Dim update_sql
    Dim update_fields()
    field_count = 0
    
    '动态构建更新字段
    If riqi <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[日期] = '" & riqi & "'"
        field_count = field_count + 1
    End If
    
    If xingming <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[姓名] = '" & xingming & "'"
        field_count = field_count + 1
    End If
    
    If zhongwen_xingming <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[中文姓名] = '" & zhongwen_xingming & "'"
        field_count = field_count + 1
    End If
    
    If guoqi_renwu_shuliang <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[过期任务数量] = " & guoqi_renwu_shuliang
        field_count = field_count + 1
    End If
    
    If koufen <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[扣分] = " & koufen
        field_count = field_count + 1
    End If
    
    If koufen_mingxi <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[扣分明细] = '" & koufen_mingxi & "'"
        field_count = field_count + 1
    End If
    
    If koufen_xitong <> "" Then
        ReDim Preserve update_fields(field_count)
        update_fields(field_count) = "[扣分系统] = '" & koufen_xitong & "'"
        field_count = field_count + 1
    End If
    
    '检查是否有字段需要更新
    If field_count = 0 Then
        Response.Write "{""code"":-1,""msg"":""没有需要更新的字段""}"
        Response.End
    End If
    
    '构建完整的更新SQL
    update_sql = "UPDATE [待办任务扣分表] SET " & Join(update_fields, ", ") & " WHERE id = " & record_id
    
    '执行更新操作
    On Error Resume Next
    conn.Execute update_sql
    If Err.Number <> 0 Then
        Response.Write "{""code"":-1,""msg"":""更新失败: " & Err.Description & """}"
        Response.End
    End If
    On Error GoTo 0
    
    ' 返回成功响应
    Response.Write "{""code"":1,""msg"":""更新记录成功""}"

'删除记录操作
ElseIf operation = "delete" Then
    '检查必填参数
    If record_id = "" Then
        Response.Write "{""code"":-1, ""msg"":""缺少记录ID参数""}"
        Response.End
    End If
    
    '先检查要删除的记录是否存在
    Dim rsCheckDel, sqlDel
    sqlDel = "SELECT COUNT(*) FROM [待办任务扣分表] WHERE id=" & record_id
    Set rsCheckDel = conn.Execute(sqlDel)
    If rsCheckDel(0) = 0 Then
        Response.Write "{""code"":-1,""msg"":""要删除的记录不存在""}"
        Response.End
    End If
    Set rsCheckDel = Nothing
    
    '构建删除SQL
    Dim delete_sql
    delete_sql = "DELETE FROM [待办任务扣分表] WHERE id = " & record_id
    
    '执行删除操作
    On Error Resume Next
    conn.Execute delete_sql
    If Err.Number <> 0 Then
        Response.Write "{""code"":-1,""msg"":""删除失败: " & Err.Description & """}"
        Response.End
    End If
    On Error GoTo 0
    
    ' 返回成功响应
    Response.Write "{""code"":1,""msg"":""删除记录成功""}"

'查询记录操作
ElseIf operation = "query" Then
    '构建查询SQL
    Dim query_sql, rs
    Dim where_conditions()
    Dim condition_count
    condition_count = 0
    
    '动态构建查询条件
    If record_id <> "" Then
        ReDim Preserve where_conditions(condition_count)
        where_conditions(condition_count) = "id = " & record_id
        condition_count = condition_count + 1
    End If
    
    If xingming <> "" Then
        ReDim Preserve where_conditions(condition_count)
        where_conditions(condition_count) = "[姓名] LIKE '%" & xingming & "%'"
        condition_count = condition_count + 1
    End If
    
    If riqi <> "" Then
        ReDim Preserve where_conditions(condition_count)
        where_conditions(condition_count) = "[日期] = '" & riqi & "'"
        condition_count = condition_count + 1
    End If
    
    '构建完整的查询SQL
    query_sql = "SELECT * FROM [待办任务扣分表]"
    If condition_count > 0 Then
        query_sql = query_sql & " WHERE " & Join(where_conditions, " AND ")
    End If
    query_sql = query_sql & " ORDER BY id DESC"
    
    '执行查询操作
    On Error Resume Next
    Set rs = conn.Execute(query_sql)
    If Err.Number <> 0 Then
        Response.Write "{""code"":-1,""msg"":""查询失败: " & Err.Description & """}"
        Response.End
    End If
    On Error GoTo 0
    
    '构建JSON响应
    Dim json_data, record_count
    json_data = "["
    record_count = 0
    
    Do While Not rs.EOF
        If record_count > 0 Then json_data = json_data & ","
        json_data = json_data & "{"
        json_data = json_data & """id"":" & rs("id") & ","
        json_data = json_data & """riqi"":""" & rs("日期") & ""","
        json_data = json_data & """xingming"":""" & rs("姓名") & ""","
        json_data = json_data & """zhongwen_xingming"":""" & rs("中文姓名") & ""","
        json_data = json_data & """guoqi_renwu_shuliang"":" & rs("过期任务数量") & ","
        json_data = json_data & """koufen"":" & rs("扣分") & ","
        json_data = json_data & """koufen_mingxi"":""" & rs("扣分明细") & ""","
        json_data = json_data & """koufen_xitong"":""" & rs("扣分系统") & """"
        json_data = json_data & "}"
        record_count = record_count + 1
        rs.MoveNext
    Loop
    
    json_data = json_data & "]"
    Set rs = Nothing
    
    ' 返回查询结果
    Response.Write "{""code"":1,""msg"":""查询成功"",""data"":" & json_data & "}"

Else
    Response.Write "{""code"":-1,""msg"":""不支持的操作类型""}"
End If
%>
